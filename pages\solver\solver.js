// pages/solver/solver.js
const app = getApp()

Page({
  data: {
    step: 'upload', // upload, confirm, analyze, solve
    problemImage: '',
    problemText: '',
    isLoading: false,
    loadingText: '正在处理...',
    analysisResult: null,
    currentQuestion: '',
    currentAnswer: '',
    conversation: [],
    highlightedText: '',
    isThinking: false,
    showHint: false,
    hintContent: '',
    showExample: false,
    exampleContent: '',
    showKnowledge: false,
    knowledgeContent: ''
  },

  onLoad: function (options) {
    // Check if user can use the app
    if (!app.checkUsageLimit()) {
      wx.navigateTo({
        url: '/pages/payment/payment',
      });
      return;
    }
  },

  // Upload problem methods
  chooseImage: function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 直接使用公式识别，不显示选项菜单
        this.setData({
          problemImage: tempFilePath,
          isLoading: true,
          loadingText: '正在识别数学公式...'
        });
        this.recognizeImage(tempFilePath, 'mathOCR');
      }
    });
  },

  // 检测识别是否不完整（改进版）
  isIncompleteRecognition: function(text) {
    // 检查文本是否过短
    if (!text || text.length < 30) {
      return true;
    }

    // 检查是否只有题目描述，缺少问题部分
    const hasQuestionMarkers = text.includes('(1)') || text.includes('（1）') ||
                              text.includes('(2)') || text.includes('（2）') ||
                              text.includes('求') || text.includes('计算') ||
                              text.includes('解') || text.includes('证明') ||
                              text.includes('函数关系式') || text.includes('最大值') ||
                              text.includes('最小值') || text.includes('？') || text.includes('?');

    // 检查是否包含应用题的典型特征
    const hasApplicationFeatures = text.includes('某地') || text.includes('政府') ||
                                  text.includes('广场') || text.includes('面积') ||
                                  text.includes('平方米') || text.includes('设计');

    // 如果是应用题但没有问题标记，可能识别不完整
    if (hasApplicationFeatures && !hasQuestionMarkers && text.length > 50) {
      console.log('检测到应用题特征但缺少问题部分，可能识别不完整');
      return true;
    }

    // 如果文本很长但没有问题标记，可能识别不完整
    if (text.length > 150 && !hasQuestionMarkers) {
      console.log('文本较长但缺少问题标记，可能识别不完整');
      return true;
    }

    return false;
  },

  // 尝试使用通用OCR作为备用方案
  tryGeneralOCR: function(fileID) {
    console.log('使用通用OCR重新识别');

    wx.showToast({
      title: '尝试通用识别...',
      icon: 'loading',
      duration: 2000
    });

    wx.cloud.callFunction({
      name: 'tencentOCR',
      data: {
        fileID: fileID
      },
      success: result => {
        console.log('通用OCR结果:', result);

        const ocrResult = result.result;
        if (ocrResult && ocrResult.TextDetections && ocrResult.TextDetections.length > 0) {
          // 按照位置排序并组合文本
          let fullText = this.combineOCRText(ocrResult.TextDetections);

          console.log('通用OCR识别到的完整文本:', fullText);

          wx.showToast({
            title: '识别成功',
            icon: 'success',
            duration: 1000
          });

          this.setData({
            problemText: fullText,
            isLoading: false,
            step: 'confirm'
          });
        } else {
          this.handleOCRFailure();
        }
      },
      fail: err => {
        console.error('通用OCR失败:', err);
        this.handleOCRFailure();
      }
    });
  },

  // 组合OCR文本
  combineOCRText: function(textDetections) {
    // 按照 Y 坐标排序，确保文本按照正确的顺序组合
    const sortedDetections = [...textDetections].sort((a, b) => {
      // 如果 Y 坐标相差不大，则按照 X 坐标排序
      if (Math.abs(a.ItemPolygon.Y - b.ItemPolygon.Y) < 10) {
        return a.ItemPolygon.X - b.ItemPolygon.X;
      }
      return a.ItemPolygon.Y - b.ItemPolygon.Y;
    });

    let currentY = -1;
    let currentLine = '';
    let fullText = '';

    // 按行组织文本
    sortedDetections.forEach(item => {
      if (currentY === -1 || Math.abs(item.ItemPolygon.Y - currentY) > 10) {
        // 新的一行
        if (currentLine) {
          fullText += currentLine + '\n';
        }
        currentLine = item.DetectedText;
        currentY = item.ItemPolygon.Y;
      } else {
        // 同一行
        currentLine += ' ' + item.DetectedText;
      }
    });

    // 添加最后一行
    if (currentLine) {
      fullText += currentLine;
    }

    return fullText.trim();
  },

  // 处理OCR失败
  handleOCRFailure: function() {
    wx.showModal({
      title: '识别失败',
      content: '未能识别出题目内容，请尝试以下方法：\n1. 确保图片清晰\n2. 尝试裁剪图片，只保留题目部分\n3. 手动输入题目',
      showCancel: true,
      cancelText: '重试',
      confirmText: '手动输入',
      success: (res) => {
        if (res.confirm) {
          // 用户选择手动输入
          this.setData({
            problemText: '',
            isLoading: false,
            step: 'confirm'
          });
        } else {
          // 用户选择重试
          this.setData({
            isLoading: false
          });
        }
      }
    });
  },

  recognizeImage: function (filePath, functionName = 'mathOCR') {
    // Upload image to cloud storage first
    const cloudPath = `problems/${Date.now()}-${Math.floor(Math.random() * 1000)}.jpg`;

    console.log('开始上传图片到云存储:', cloudPath);

    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: res => {
        console.log('图片上传成功，fileID:', res.fileID);

        // 显示上传的图片ID，方便调试
        wx.showToast({
          title: '图片上传成功',
          icon: 'none',
          duration: 1000
        });

        // Call OCR cloud function
        console.log(`开始调用OCR云函数: ${functionName}`);
        wx.cloud.callFunction({
          name: functionName, // 使用传入的云函数名称
          data: {
            fileID: res.fileID
          },
          success: result => {
            console.log('OCR云函数调用成功，结果:', result);

            const ocrResult = result.result;
            console.log('OCR结果详情:', JSON.stringify(ocrResult, null, 2));
            // 输出完整的result对象，可能包含更多错误信息
            console.log('完整的OCR返回结果:', JSON.stringify(result, null, 2));

            // 处理各种可能的返回格式
            console.log('开始处理OCR结果，类型:', typeof ocrResult);

            // 优先处理新版本的OCR结果
            if (ocrResult && ocrResult.ocrText) {
              // 使用OCR识别接口返回的文本
              const formulaText = ocrResult.ocrText;

              console.log('识别到的文本(ocrText):', formulaText);
              console.log('OCR版本:', ocrResult.version);
              console.log('是否为应用题:', ocrResult.isApplicationProblem);
              console.log('识别是否完整:', ocrResult.isComplete);

              // 检查是否识别不完整
              if (this.isIncompleteRecognition(formulaText) || ocrResult.isComplete === false) {
                console.log('检测到识别不完整，尝试通用OCR');
                this.tryGeneralOCR(res.fileID);
                return;
              }

              // 显示成功提示
              wx.showToast({
                title: ocrResult.isApplicationProblem ? '应用题识别成功' : '公式识别成功',
                icon: 'success',
                duration: 1000
              });

              this.setData({
                problemText: formulaText,
                isLoading: false,
                step: 'confirm'
              });
            } else if (ocrResult && ocrResult.FormulaInfoList && ocrResult.FormulaInfoList.length > 0) {
              // 处理直接从API返回的FormulaInfoList格式
              const formulaText = ocrResult.FormulaInfoList[0].DetectedText;

              console.log('识别到的数学公式(FormulaInfoList):', formulaText);

              // 显示成功提示
              wx.showToast({
                title: '公式识别成功',
                icon: 'success',
                duration: 1000
              });

              // 处理LaTeX格式，去除多余的反斜杠和格式化公式
              let cleanedText = formulaText.replace(/\\\\/g, '\\');

              // 确保公式不包含多余的括号和命令
              cleanedText = this.formatLatexFormula(cleanedText);

              this.setData({
                problemText: cleanedText,
                isLoading: false,
                step: 'confirm'
              });
            } else if (ocrResult && ocrResult.TextDetections && ocrResult.TextDetections.length > 0) {
              // 兼容通用文字识别接口
              let fullText = '';

              // 按照 Y 坐标排序，确保文本按照正确的顺序组合
              const sortedDetections = [...ocrResult.TextDetections].sort((a, b) => {
                // 如果 Y 坐标相差不大，则按照 X 坐标排序
                if (Math.abs(a.ItemPolygon.Y - b.ItemPolygon.Y) < 10) {
                  return a.ItemPolygon.X - b.ItemPolygon.X;
                }
                return a.ItemPolygon.Y - b.ItemPolygon.Y;
              });

              let currentY = -1;
              let currentLine = '';

              // 按行组织文本
              sortedDetections.forEach(item => {
                if (currentY === -1 || Math.abs(item.ItemPolygon.Y - currentY) > 10) {
                  // 新的一行
                  if (currentLine) {
                    fullText += currentLine + '\n';
                  }
                  currentLine = item.DetectedText;
                  currentY = item.ItemPolygon.Y;
                } else {
                  // 同一行
                  currentLine += ' ' + item.DetectedText;
                }
              });

              // 添加最后一行
              if (currentLine) {
                fullText += currentLine;
              }

              console.log('识别到的文本:', fullText);

              this.setData({
                problemText: fullText.trim(),
                isLoading: false,
                step: 'confirm'
              });
            } else if (ocrResult && ocrResult.FormulaInfos && ocrResult.FormulaInfos.length > 0) {
              // 直接处理腾讯OCR返回的FormulaInfos
              const formulaText = ocrResult.FormulaInfos[0].DetectedText;

              console.log('识别到的数学公式(FormulaInfos):', formulaText);

              // 显示成功提示
              wx.showToast({
                title: '公式识别成功',
                icon: 'success',
                duration: 1000
              });

              this.setData({
                problemText: formulaText,
                isLoading: false,
                step: 'confirm'
              });
            } else {
              console.error('OCR结果异常:', ocrResult);

              // 显示更友好的错误提示和选项
              wx.showModal({
                title: '识别失败',
                content: '未能识别出数学公式，请尝试以下方法：\n1. 确保图片中包含清晰的数学公式\n2. 尝试裁剪图片，只保留公式部分\n3. 手动输入公式',
                showCancel: true,
                cancelText: '重试',
                confirmText: '手动输入',
                success: (res) => {
                  if (res.confirm) {
                    // 用户选择手动输入
                    this.setData({
                      problemText: '',
                      isLoading: false,
                      step: 'confirm'
                    });
                  } else {
                    // 用户选择重试
                    this.setData({
                      isLoading: false
                    });
                  }
                }
              });
            }
          },
          fail: err => {
            console.error('OCR云函数调用失败:', err);
            this.handleError('OCR服务调用失败: ' + (err.errMsg || JSON.stringify(err)));
          }
        });
      },
      fail: err => {
        console.error('图片上传失败:', err);
        this.handleError('上传图片失败: ' + (err.errMsg || JSON.stringify(err)));
      }
    });
  },

  // Confirm problem methods
  editProblem: function (e) {
    // 从textarea输入时更新公式
    if (e && e.detail && e.detail.value !== undefined) {
      this.setData({
        problemText: e.detail.value
      });
    }
  },

  // 显示编辑模态框
  showEditModal: function () {
    const that = this;
    wx.showModal({
      title: '编辑题目',
      content: '请输入或修改题目内容',
      editable: true,
      placeholderText: '请输入LaTeX格式的数学公式',
      value: that.data.problemText,
      success: function(res) {
        if (res.confirm && res.content) {
          that.setData({
            problemText: res.content
          });
        }
      }
    });
  },

  confirmProblem: function () {
    if (!this.data.problemText.trim()) {
      wx.showToast({
        title: '题目内容不能为空',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true,
      loadingText: '正在分析题目...',
      step: 'analyze'
    });

    // Increment usage count
    const newCount = app.incrementUsage();

    // Call DeepSeek cloud function to analyze the problem
    wx.cloud.callFunction({
      name: 'deepseek',
      data: {
        action: 'analyze',
        problem: this.data.problemText,
        userInfo: app.globalData.userInfo
      },
      success: result => {
        const analysisResult = result.result;

        this.setData({
          analysisResult: analysisResult,
          highlightedText: analysisResult.highlightedText || this.data.problemText,
          isLoading: false,
          step: 'solve',
          conversation: [{
            role: 'system',
            content: '我将帮助你解决这道数学题。让我们一步步来思考。'
          }]
        });

        // Start the Socratic questioning
        this.startSocraticQuestioning();
      },
      fail: err => {
        this.handleError('分析题目失败: ' + err.errMsg);
      }
    });
  },

  // Solve problem methods
  startSocraticQuestioning: function () {
    this.setData({
      isThinking: true
    });

    // Call DeepSeek cloud function to get the first question
    wx.cloud.callFunction({
      name: 'deepseek',
      data: {
        action: 'question',
        problem: this.data.problemText,
        conversation: this.data.conversation,
        userInfo: app.globalData.userInfo
      },
      success: result => {
        const question = result.result.question;

        // Add the question to the conversation
        const updatedConversation = [...this.data.conversation, {
          role: 'assistant',
          content: question
        }];

        this.setData({
          currentQuestion: question,
          conversation: updatedConversation,
          isThinking: false
        });
      },
      fail: err => {
        this.handleError('获取问题失败: ' + err.errMsg);
      }
    });
  },

  submitAnswer: function (e) {
    const answer = e.detail.value;

    if (!answer.trim()) {
      wx.showToast({
        title: '请输入回答',
        icon: 'none'
      });
      return;
    }

    // Add the user's answer to the conversation
    const updatedConversation = [...this.data.conversation, {
      role: 'user',
      content: answer
    }];

    this.setData({
      currentAnswer: '',
      conversation: updatedConversation,
      isThinking: true
    });

    // Call DeepSeek cloud function to get the next question or feedback
    wx.cloud.callFunction({
      name: 'deepseek',
      data: {
        action: 'question',
        problem: this.data.problemText,
        conversation: updatedConversation,
        userInfo: app.globalData.userInfo
      },
      success: result => {
        const response = result.result;

        // Add the response to the conversation
        const newConversation = [...this.data.conversation, {
          role: 'assistant',
          content: response.question
        }];

        this.setData({
          currentQuestion: response.question,
          conversation: newConversation,
          isThinking: false
        });

        // Check if we've reached the end of the solution
        if (response.isSolved) {
          this.handleSolutionComplete();
        }
      },
      fail: err => {
        this.handleError('获取回应失败: ' + err.errMsg);
      }
    });
  },

  handleSolutionComplete: function () {
    wx.showToast({
      title: '解题完成！',
      icon: 'success'
    });

    // You could add additional logic here for when the solution is complete
  },

  // Helper buttons methods
  showHintContent: function () {
    this.setData({
      isThinking: true,
      showHint: false
    });

    wx.cloud.callFunction({
      name: 'deepseek',
      data: {
        action: 'hint',
        problem: this.data.problemText,
        conversation: this.data.conversation,
        userInfo: app.globalData.userInfo
      },
      success: result => {
        this.setData({
          hintContent: result.result.hint,
          showHint: true,
          isThinking: false
        });
      },
      fail: err => {
        this.handleError('获取提示失败: ' + err.errMsg);
      }
    });
  },

  showExampleContent: function () {
    this.setData({
      isThinking: true,
      showExample: false
    });

    wx.cloud.callFunction({
      name: 'deepseek',
      data: {
        action: 'example',
        problem: this.data.problemText,
        conversation: this.data.conversation,
        userInfo: app.globalData.userInfo
      },
      success: result => {
        this.setData({
          exampleContent: result.result.example,
          showExample: true,
          isThinking: false
        });
      },
      fail: err => {
        this.handleError('获取例题失败: ' + err.errMsg);
      }
    });
  },

  showKnowledgeContent: function () {
    this.setData({
      isThinking: true,
      showKnowledge: false
    });

    wx.cloud.callFunction({
      name: 'deepseek',
      data: {
        action: 'knowledge',
        problem: this.data.problemText,
        conversation: this.data.conversation,
        userInfo: app.globalData.userInfo
      },
      success: result => {
        this.setData({
          knowledgeContent: result.result.knowledge,
          showKnowledge: true,
          isThinking: false
        });
      },
      fail: err => {
        this.handleError('获取知识点失败: ' + err.errMsg);
      }
    });
  },

  closeHint: function () {
    this.setData({
      showHint: false
    });
  },

  closeExample: function () {
    this.setData({
      showExample: false
    });
  },

  closeKnowledge: function () {
    this.setData({
      showKnowledge: false
    });
  },

  // 格式化 LaTeX 公式
  formatLatexFormula: function(formula) {
    if (!formula) return formula;

    console.log('原始公式:', formula);

    // 检查是否为应用题（包含中文字符）
    if (/[\u4e00-\u9fa5]/.test(formula)) {
      // 对于应用题，进行特殊处理
      let processed = formula;

      // 首先处理可能的错误格式，如 \$$ 和 $$\
      processed = processed.replace(/\\\$\$/g, '');  // 移除 \$$
      processed = processed.replace(/\$\$\\/g, '');  // 移除 $$\

      // 处理 LaTeX 命令中的特殊字符
      // 处理 \begin{align*} 和 \end{align*} 标记
      processed = processed.replace(/\\begin\{align\*\}([\s\S]*?)\\end\{align\*\}/g, (match, p1) => {
        return `$$${p1}$$`;
      });

      // 处理 \text{} 命令，保留其中的文本
      processed = processed.replace(/\\text\{([^{}]*)\}/g, (match, p1) => {
        return p1;
      });

      // 处理 \quad 命令，替换为空格
      processed = processed.replace(/\\quad/g, ' ');

      // 处理 \end{align} 命令
      processed = processed.replace(/\\end\{align\}/g, '');

      // 处理 \frac{}{} 命令，将其包装在 $$ $$ 中
      processed = processed.replace(/(\\frac\{[^{}]+\}\{[^{}]+\})/g, (match) => {
        return `$$${match}$$`;
      });

      // 处理简单的数学表达式，如 v+m, v-m 等
      processed = processed.replace(/([a-zA-Z])([+\-*/])([a-zA-Z])/g, (match) => {
        return `$$${match}$$`;
      });

      // 处理选项标记，如 A. B. C. D.，确保格式正确
      processed = processed.replace(/([A-D])\.\s*/g, '\n\n$1. ');

      // 处理特殊的公式格式，如 \frac{v+m}{v-m}
      processed = processed.replace(/\$\$\\frac\{([^{}]+)\}\{([^{}]+)\}\$\$/g, (match, p1, p2) => {
        return `$$\\frac{${p1}}{${p2}}$$`;
      });

      // 移除多余的反斜杠
      processed = processed.replace(/\\([^a-zA-Z{}])/g, '$1');

      // 处理换行，确保选项之间有适当的间距
      processed = processed.replace(/\n\s*\n/g, '\n\n');

      // 确保题目开头有适当的格式
      processed = processed.trim();

      // 特殊处理：检查是否包含 [SS 和 SS] 格式的内容
      if (formula.includes('[SS') && formula.includes('SS]')) {
        // 提取 [SS 和 SS] 之间的内容
        const regex = /\[SS(.*?)SS\]/g;
        const matches = formula.match(regex);

        if (matches && matches.length > 0) {
          // 将每个匹配项转换为 Markdown 格式的公式
          matches.forEach(match => {
            const content = match.replace('[SS', '').replace('SS]', '');
            processed = processed.replace(match, `$$${content}$$`);
          });
        }
      }

      // 处理 \$frac 格式的公式
      processed = processed.replace(/\\\$frac/g, '$$\\frac');
      processed = processed.replace(/\$\$\$/g, '$$');

      // 处理 OCR 识别出的特殊格式
      processed = processed.replace(/\$frac/g, '$$\\frac');

      console.log('处理后的应用题文本:', processed);

      return processed;
    }

    // 处理纯数学公式
    // 直接返回原始公式，让 math-formula 组件处理
    // 这样可以确保公式渲染的一致性
    return formula;
  },

  // Error handling
  handleError: function (errorMsg) {
    this.setData({
      isLoading: false,
      isThinking: false
    });

    wx.showToast({
      title: errorMsg,
      icon: 'none',
      duration: 2000
    });
  },

  // Navigation methods
  goBack: function () {
    if (this.data.step === 'confirm') {
      this.setData({
        step: 'upload',
        problemImage: '',
        problemText: ''
      });
    } else if (this.data.step === 'analyze' || this.data.step === 'solve') {
      wx.showModal({
        title: '确认返回',
        content: '返回将丢失当前解题进度，确定要返回吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              step: 'upload',
              problemImage: '',
              problemText: '',
              analysisResult: null,
              conversation: [],
              currentQuestion: '',
              currentAnswer: ''
            });
          }
        }
      });
    }
  }
})
