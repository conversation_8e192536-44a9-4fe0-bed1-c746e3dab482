<!--components/math-formula/math-formula.wxml-->
<view class="math-formula-container">
  <view wx:if="{{isLoading}}" class="loading">加载中...</view>
  <view wx:elif="{{errorMessage}}" class="error-message">{{errorMessage}}</view>

  <!-- 优化的结构化题目显示 -->
  <block wx:elif="{{isApplicationProblem && problemText && options.length > 0}}">
    <view class="structured-math-problem">
      <!-- 题目标题区域 -->
      <view class="problem-header">
        <text class="problem-title">题目</text>
      </view>

      <!-- 题目内容区域 -->
      <view class="problem-content-area">
        <text class="problem-text">{{problemText}}</text>
      </view>

      <!-- 选项区域 -->
      <view class="options-section">
        <view class="option-row" wx:for="{{options}}" wx:key="index">
          <view class="option-label-container">
            <text class="option-label">{{item.label}}</text>
          </view>
          <view class="option-content">
            <!-- 渲染的数学公式显示 -->
            <view class="option-formula-display">
              <text class="math-formula" decode="{{true}}">{{item.displayText}}</text>
            </view>


          </view>
        </view>
      </view>


    </view>
  </block>

  <!-- 使用towxml渲染内容 -->
  <block wx:elif="{{article && (article.children || article.child)}}">
    <towxml nodes="{{article}}" />
  </block>

  <!-- 简单应用题显示 -->
  <block wx:elif="{{isApplicationProblem}}">
    <view class="application-problem">
      <view class="simple-text">{{formattedText}}</view>
    </view>
  </block>

  <!-- 纯公式图片显示 -->
  <block wx:elif="{{imageUrl}}">
    <image
      class="formula-image"
      src="{{imageUrl}}"
      mode="widthFix"
      binderror="onImageError"
      bindload="onImageLoad"
    ></image>
  </block>

  <!-- 空状态 -->
  <view wx:else class="empty-formula">{{formula || '暂无公式'}}</view>
</view>
