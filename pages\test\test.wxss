/* pages/test/test.wxss */
.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #fff;
  padding: 40rpx 30rpx 30rpx 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.test-section {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.section-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.test-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 26rpx;
  padding: 0;
  margin: 0;
  background-color: #007aff;
  color: white;
  border-radius: 8rpx;
}

.test-btn:active {
  background-color: #0056cc;
}

.formula-container {
  padding: 30rpx;
  background-color: #fff;
}

.input-area {
  padding: 30rpx;
  background-color: #fff;
}

.custom-input {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.custom-input:focus {
  border-color: #007aff;
  background-color: #fff;
}
