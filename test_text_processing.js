// 测试文本处理函数
function processApplicationProblemText(text) {
  let processedText = text;

  console.log('原始文本:', processedText);

  // 1. 处理可能的错误格式
  processedText = processedText.replace(/\\\$\$/g, '');  // 移除 \$$
  processedText = processedText.replace(/\$\$\\/g, '');  // 移除 $$\

  // 2. 处理LaTeX命令
  processedText = processedText.replace(/\\text\{([^{}]*)\}/g, '$1');
  processedText = processedText.replace(/\\quad/g, ' ');
  processedText = processedText.replace(/\\begin\{align\*?\}|\\end\{align\*?\}/g, '');

  // 3. 首先清理所有换行符，重新组织文本
  processedText = processedText.replace(/\s*\n\s*/g, ' ');
  processedText = processedText.replace(/\s+/g, ' ');

  // 4. 先处理问题编号，确保前后有适当的空行
  processedText = processedText.replace(/\s*[（(](\d+)[）)]\s*/g, '\n\n($1) ');

  // 5. 智能断句：在句号、感叹号、问号后添加换行
  // 但要避免在问题编号、数字、小数点等情况下断句
  processedText = processedText.replace(/([。！？])\s*(?!\s*$)(?![）)])/g, '$1\n\n');
  
  // 6. 特殊处理：确保在"平方米。"后面有换行
  processedText = processedText.replace(/(平方米[。！？])\s*(?!\s*$)/g, '$1\n\n');

  // 7. 处理选项标记
  processedText = processedText.replace(/([A-D])\.\s*/g, '\n\n$1. ');

  // 8. 处理题目编号（如"1、"）
  processedText = processedText.replace(/^(\d+[、.])\s*/g, '$1');

  // 9. 清理多余的空行，但保持双换行
  processedText = processedText.replace(/\n\s*\n\s*\n+/g, '\n\n');

  // 10. 确保开头没有多余的空行
  processedText = processedText.replace(/^\s*\n+/, '');

  // 11. 确保结尾没有多余的空行
  processedText = processedText.replace(/\s*$/, '');

  console.log('处理后文本:', processedText);

  return processedText.trim();
}

// 测试用例
const testText = "1、某地方政府准备在一块面积足够大的荒地上建一如图所示的一个矩形综合性休闲广场，其总面积为3000平方米，其中场地四周（阴影部分）为通道，通道宽度均为2米，中间的三个矩形区域将铺设塑胶地面作为运动场地（其中两个小场地形状相同），塑胶运动场地占地面积为S平方米。(1) 分别写出用x表示y和S的函数关系式（写出函数定义域）；(2) 怎样设计能使S取得最大值，最大值为多少？";

console.log("=== 测试智能断句功能 ===");
const result = processApplicationProblemText(testText);
console.log("\n=== 最终结果 ===");
console.log(result);
