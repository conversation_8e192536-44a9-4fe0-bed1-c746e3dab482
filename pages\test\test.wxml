<!--pages/test/test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">数学题目显示测试</text>
    <text class="subtitle">测试不同类型的数学题目渲染效果</text>
  </view>

  <scroll-view class="content" scroll-y="true">
    <!-- 轮船题目测试 -->
    <view class="test-section">
      <view class="section-title">
        <text class="section-name">轮船题目测试</text>
        <button class="test-btn" bindtap="testShipProblem">测试</button>
      </view>
      <view class="formula-container" wx:if="{{showShipProblem}}">
        <math-formula formula="{{shipProblemText}}"></math-formula>
      </view>
    </view>

    <!-- 复杂应用题测试 -->
    <view class="test-section">
      <view class="section-title">
        <text class="section-name">复杂应用题测试</text>
        <button class="test-btn" bindtap="testComplexProblem">测试</button>
      </view>
      <view class="formula-container" wx:if="{{showComplexProblem}}">
        <math-formula formula="{{complexProblemText}}"></math-formula>
      </view>
    </view>

    <!-- 三角函数题目测试 -->
    <view class="test-section">
      <view class="section-title">
        <text class="section-name">三角函数题目测试</text>
        <button class="test-btn" bindtap="testTrigProblem">测试</button>
      </view>
      <view class="formula-container" wx:if="{{showTrigProblem}}">
        <math-formula formula="{{trigProblemText}}"></math-formula>
      </view>
    </view>

    <!-- 简单数学公式测试 -->
    <view class="test-section">
      <view class="section-title">
        <text class="section-name">简单数学公式测试</text>
        <button class="test-btn" bindtap="testSimpleMath">测试</button>
      </view>
      <view class="formula-container" wx:if="{{showSimpleMath}}">
        <math-formula formula="{{simpleMathText}}"></math-formula>
      </view>
    </view>

    <!-- 自定义输入测试 -->
    <view class="test-section">
      <view class="section-title">
        <text class="section-name">自定义输入测试</text>
      </view>
      <view class="input-area">
        <textarea
          class="custom-input"
          placeholder="请输入数学题目内容..."
          value="{{customInput}}"
          bindinput="onCustomInput"
        ></textarea>
        <button class="test-btn" bindtap="testCustomInput">测试渲染</button>
      </view>
      <view class="formula-container" wx:if="{{showCustom}}">
        <math-formula formula="{{customInput}}"></math-formula>
      </view>
    </view>
  </scroll-view>
</view>
