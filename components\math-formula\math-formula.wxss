/* components/math-formula/math-formula.wxss */
.math-formula-container {
  display: block;
  width: 100%;
  overflow-x: auto;
  padding: 5rpx 0;
  text-align: left;
}

.loading {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 10rpx;
}

.error-message {
  font-size: 28rpx;
  color: #e64340;
  text-align: center;
  padding: 10rpx;
}

.empty-formula {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  padding: 20rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  margin: 10rpx 0;
}

.formula-image {
  max-width: 100%;
  margin: 10rpx auto;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 8rpx;
}

.application-problem {
  font-size: 30rpx;
  color: #333;
  text-align: left;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin: 10rpx 0;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* towxml 样式覆盖 */
.math-formula-container .h2w {
  text-align: left !important;
  font-size: 30rpx !important;
  line-height: 1.6 !important;
}

.math-formula-container .h2w__p {
  margin: 10rpx 0 !important;
  text-align: left !important;
}

.math-formula-container .h2w__latex {
  display: inline-block !important;
  margin: 5rpx !important;
}

/* 优化的结构化题目样式 */
.structured-math-problem {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0;
  margin: 10rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.problem-header {
  background-color: #f8f9fa;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.problem-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.problem-content-area {
  padding: 30rpx;
  background-color: #fff;
}

.problem-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  white-space: pre-line;
  word-wrap: break-word;
}

.options-section {
  padding: 20rpx 30rpx 30rpx 30rpx;
  background-color: #fff;
}

.option-row {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 0;
}

.option-row:last-child {
  margin-bottom: 0;
}

.option-label-container {
  min-width: 80rpx;
  margin-right: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80rpx;
}

.option-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.option-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.option-formula-display {
  width: 100%;
  min-height: 80rpx;
  background-color: #fff;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.option-formula-display:hover {
  border-color: #007aff;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);
}

.math-formula {
  font-size: 36rpx;
  color: #333;
  font-family: 'Times New Roman', serif;
  line-height: 1.4;
  text-align: center;
  font-weight: 500;
  white-space: pre-line;
}



.option-fallback-text {
  font-size: 28rpx;
  color: #333;
  font-family: 'Times New Roman', serif;
  background-color: #fff;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  border: 1rpx solid #e9ecef;
  min-height: 40rpx;
  display: flex;
  align-items: center;
}

/* 原有的结构化题目样式（保持兼容性） */
.structured-problem {
  padding: 20rpx;
}

.options-container {
  margin-top: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  margin: 15rpx 0;
  padding: 10rpx 0;
}

.formula-image-inline {
  width: auto;
  height: auto;
  max-width: 400rpx;
  max-height: 120rpx;
  min-width: 80rpx;
  min-height: 40rpx;
  margin: 0 15rpx;
  display: inline-block;
  vertical-align: middle;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4rpx;
  padding: 5rpx;
}

.option-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

.simple-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  white-space: pre-line;
  word-break: break-word;
  text-align: justify;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin: 10rpx 0;
}


