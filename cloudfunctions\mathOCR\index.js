// 云函数入口文件
const cloud = require('wx-server-sdk')
const tencentcloud = require('tencentcloud-sdk-nodejs')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 导入对应产品模块的client models
const ocr = tencentcloud.ocr.v20181119
const OcrClient = ocr.Client

// 创建OCR客户端
const client = new OcrClient({
  credential: {
    secretId: process.env.SecretId,
    secretKey: process.env.SecretKey,
  },
  region: 'ap-guangzhou',
  profile: {
    httpProfile: {
      endpoint: 'ocr.tencentcloudapi.com',
    },
  },
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { fileID } = event

  console.log('接收到数学公式OCR请求，fileID:', fileID);
  console.log('云函数版本: v4_enhanced_2024 - 优先使用通用文字识别');
  console.log('环境变量检查 - SecretId存在:', !!process.env.SecretId);
  console.log('环境变量检查 - SecretKey存在:', !!process.env.SecretKey);

  try {
    // 下载云存储中的图片
    console.log('开始从云存储下载图片');
    const result = await cloud.downloadFile({
      fileID: fileID,
    })

    console.log('图片下载成功，大小:', result.fileContent.length, '字节');

    const buffer = result.fileContent
    const base64Image = buffer.toString('base64')

    // 调用数学公式识别接口
    const params = {
      ImageBase64: base64Image,
    }

    // 打印请求参数（不包含图片内容）
    console.log('请求参数:', {
      ...params,
      ImageBase64: '图片内容已省略...'
    });

    try {
      // 优先尝试通用文字识别，特别是对于可能包含大量文本的应用题
      console.log('开始调用腾讯通用文字识别API（优先策略）');
      let primaryResult;
      let useGeneralOCR = true;

      try {
        primaryResult = await client.GeneralBasicOCR(params);
        console.log('通用文字识别API调用成功');
        console.log('通用识别结果长度:', primaryResult.TextDetections ? primaryResult.TextDetections.length : 0);
      } catch (generalError) {
        console.log('通用文字识别失败，尝试数学公式识别:', generalError.message);
        useGeneralOCR = false;
        // 如果通用识别失败，回退到数学公式识别
        primaryResult = await client.RecognizeFormulaOCR(params);
        console.log('数学公式识别API调用成功（回退）');
      }

      // 详细记录返回结果的结构
      console.log('返回结果类型:', typeof primaryResult);
      console.log('返回结果结构:', Object.keys(primaryResult));
      console.log('使用的识别方式:', useGeneralOCR ? '通用文字识别' : '数学公式识别');

      // 处理识别结果
      if (primaryResult.TextDetections && primaryResult.TextDetections.length > 0) {
        // 检查是否包含中文字符，如果包含，则可能是应用题
        const containsChinese = primaryResult.TextDetections.some(item =>
          /[\u4e00-\u9fa5]/.test(item.DetectedText)
        );

        if (containsChinese) {
          console.log('检测到中文字符，识别为应用题');

          // 使用改进的文本组合策略
          const fullText = combineTextDetections(primaryResult.TextDetections);

          console.log('组合后的完整文本长度:', fullText.length);
          console.log('组合后的完整文本预览:', fullText.substring(0, 200) + (fullText.length > 200 ? '...' : ''));

          // 检查是否包含数学特征和问题标记
          const hasMathFeatures = /[\d\+\-\*\/\=\(\)\[\]\{\}\\]/.test(fullText) ||
                                 /[A-D]\.|选项|求|计算|解|证明/.test(fullText);

          // 检查是否包含问题部分
          const hasQuestionPart = /\(1\)|\（1\）|\(2\)|\（2\）|求|计算|解|证明|函数关系式|最大值|最小值/.test(fullText);

          if (hasMathFeatures) {
            console.log('检测到数学应用题，包含问题部分:', hasQuestionPart);

            // 应用文本处理和格式化
            const processedText = processApplicationProblemText(fullText);

            return {
              ocrText: processedText,
              errCode: 0,
              errMsg: hasQuestionPart ? "应用题识别成功（完整）" : "应用题识别成功（可能不完整）",
              version: "v4_applicationProblem_enhanced",
              isApplicationProblem: true,
              isComplete: hasQuestionPart,
              originalLength: fullText.length
            };
          }
        }

        // 如果不是应用题，按原有逻辑处理
        let fullText = '';
        primaryResult.TextDetections.forEach(item => {
          fullText += item.DetectedText + ' ';
        });

        return {
          ocrText: fullText.trim(),
          errCode: 0,
          errMsg: "文字识别成功",
          version: "v4_generalOCR"
        };
      }

      // 检查是否有FormulaInfoList（数学公式识别结果）
      else if (primaryResult.FormulaInfoList && primaryResult.FormulaInfoList.length > 0) {
        console.log('FormulaInfoList长度:', primaryResult.FormulaInfoList.length);
        console.log('第一个公式:', primaryResult.FormulaInfoList[0].DetectedText);

        // 检查公式文本长度，如果过短可能识别不完整
        const formulaText = primaryResult.FormulaInfoList[0].DetectedText;
        if (formulaText.length < 50 && useGeneralOCR === false) {
          console.log('公式文本较短，可能识别不完整，尝试通用识别');
          // 尝试通用识别作为补充
          try {
            const generalResult = await client.GeneralBasicOCR(params);
            if (generalResult.TextDetections && generalResult.TextDetections.length > 0) {
              const fullText = combineTextDetections(generalResult.TextDetections);
              if (fullText.length > formulaText.length * 1.5) {
                console.log('通用识别获得更完整的文本');

                // 检查是否包含中文字符，如果包含则可能是应用题
                const containsChinese = /[\u4e00-\u9fa5]/.test(fullText);

                if (containsChinese) {
                  return {
                    ocrText: processApplicationProblemText(fullText),
                    errCode: 0,
                    errMsg: "应用题识别成功（通用补充）",
                    version: "v4_mixed_recognition",
                    isApplicationProblem: true
                  };
                } else {
                  // 纯数学公式，不进行应用题处理
                  return {
                    ocrText: fullText,
                    errCode: 0,
                    errMsg: "数学公式识别成功（通用补充）",
                    version: "v4_mixed_formula_recognition"
                  };
                }
              }
            }
          } catch (e) {
            console.log('通用识别补充失败:', e.message);
          }
        }

        return {
          ocrText: formulaText,
          errCode: 0,
          errMsg: "数学公式识别成功",
          version: "v4_formulaOCR"
        };
      }

      // 检查是否有FormulaInfos（旧版API返回格式）
      else if (primaryResult.FormulaInfos && primaryResult.FormulaInfos.length > 0) {
        console.log('FormulaInfos长度:', primaryResult.FormulaInfos.length);
        console.log('第一个公式:', primaryResult.FormulaInfos[0].DetectedText);

        return {
          ocrText: primaryResult.FormulaInfos[0].DetectedText,
          errCode: 0,
          errMsg: "数学公式识别成功",
          version: "v4_formulaOCR_legacy"
        };
      }

      else {
        // 如果没有识别到内容，返回空结果
        return {
          ocrText: "",
          errCode: 0,
          errMsg: "未识别到文本内容",
          version: "v4_no_result",
          rawResult: JSON.stringify(primaryResult)
        };
      }
    } catch (apiError) {
      console.error('API调用出错:', apiError);

      // 尝试使用通用文字识别作为备选
      try {
        console.log('尝试使用通用文字识别作为备选');
        const generalResult = await client.GeneralBasicOCR(params);

        if (generalResult.TextDetections && generalResult.TextDetections.length > 0) {
          // 检查是否包含中文字符，如果包含，则可能是应用题
          const containsChinese = generalResult.TextDetections.some(item =>
            /[\u4e00-\u9fa5]/.test(item.DetectedText)
          );

          if (containsChinese) {
            console.log('备选识别检测到中文字符，可能是应用题');

            // 按照 Y 坐标排序，确保文本按照正确的顺序组合
            const sortedDetections = [...generalResult.TextDetections].sort((a, b) => {
              // 如果 Y 坐标相差不大，则按照 X 坐标排序
              if (Math.abs(a.ItemPolygon.Y - b.ItemPolygon.Y) < 10) {
                return a.ItemPolygon.X - b.ItemPolygon.X;
              }
              return a.ItemPolygon.Y - b.ItemPolygon.Y;
            });

            let fullText = '';
            let currentY = -1;
            let currentLine = '';

            // 按行组织文本
            sortedDetections.forEach(item => {
              if (currentY === -1 || Math.abs(item.ItemPolygon.Y - currentY) > 10) {
                // 新的一行
                if (currentLine) {
                  fullText += currentLine + '\n';
                }
                currentLine = item.DetectedText;
                currentY = item.ItemPolygon.Y;
              } else {
                // 同一行
                currentLine += ' ' + item.DetectedText;
              }
            });

            // 添加最后一行
            if (currentLine) {
              fullText += currentLine;
            }

            // 处理应用题中的LaTeX公式
            // 检查是否包含数学公式特征
            const hasMathFeatures = /[a-zA-Z][+\-*/][a-zA-Z]|\\frac|\\sqrt|\\text/.test(fullText);

            if (hasMathFeatures) {
              console.log('备选识别检测到可能包含数学公式的应用题');

              // 首先处理可能的错误格式，如 \$$ 和 $$\
              fullText = fullText.replace(/\\\$\$/g, '');  // 移除 \$$
              fullText = fullText.replace(/\$\$\\/g, '');  // 移除 $$\

              // 处理可能的LaTeX命令
              // 1. 将 \text{内容} 转换为普通文本
              fullText = fullText.replace(/\\text\{([^{}]*)\}/g, '$1');

              // 2. 处理 \quad 命令
              fullText = fullText.replace(/\\quad/g, ' ');

              // 3. 处理 \begin{align} 和 \end{align} 标记
              fullText = fullText.replace(/\\begin\{align\*?\}|\\end\{align\*?\}/g, '');

              // 4. 处理选项标记，如 A. B. C. D.
              fullText = fullText.replace(/([A-D])\.\s*/g, '$1. ');

              // 5. 处理 \frac{}{} 命令，将其包装在 $$ $$ 中
              fullText = fullText.replace(/(\\frac\{[^{}]+\}\{[^{}]+\})/g, (match) => {
                return `$$${match}$$`;
              });

              // 6. 处理简单的数学表达式，如 v+m, v-m 等
              fullText = fullText.replace(/([a-zA-Z])([+\-*/])([a-zA-Z])/g, (match) => {
                return `$$${match}$$`;
              });

              // 7. 处理特殊的公式格式，如 \frac{v+m}{v-m}
              fullText = fullText.replace(/\$\$\\frac\{([^{}]+)\}\{([^{}]+)\}\$\$/g, (match, p1, p2) => {
                return `$$\\frac{${p1}}{${p2}}$$`;
              });

              // 8. 移除多余的反斜杠
              fullText = fullText.replace(/\\([^a-zA-Z{}])/g, '$1');

              // 9. 处理 \$frac 格式的公式
              fullText = fullText.replace(/\\\$frac/g, '$$\\frac');
              fullText = fullText.replace(/\$\$\$/g, '$$');

              // 10. 处理 OCR 识别出的特殊格式
              fullText = fullText.replace(/\$frac/g, '$$\\frac');

              // 11. 将数学公式部分用 [SS...SS] 包裹，方便前端处理
              const mathParts = fullText.match(/\$\$.*?\$\$/g);
              if (mathParts && mathParts.length > 0) {
                mathParts.forEach(part => {
                  const content = part.replace(/\$\$/g, '');
                  fullText = fullText.replace(part, `[SS${content}SS]`);
                });
              }
            }

            return {
              ocrText: fullText.trim(),
              errCode: 0,
              errMsg: "应用题识别成功（备选）",
              version: "v3_applicationProblem_fallback",
              isApplicationProblem: true
            };
          } else {
            // 普通文本，可能是简单的公式
            let fullText = '';
            generalResult.TextDetections.forEach(item => {
              fullText += item.DetectedText + ' ';
            });

            return {
              ocrText: fullText.trim(),
              errCode: 0,
              errMsg: "通用文字识别成功（公式识别失败）",
              version: "v3_generalOCR_fallback"
            };
          }
        }
      } catch (fallbackError) {
        console.error('备选识别也失败:', fallbackError);
      }

      throw apiError; // 重新抛出原始错误
    }
  } catch (error) {
    console.error('OCR处理过程中出错:', error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    }
  }
}

// 辅助函数：改进的文本组合策略
function combineTextDetections(textDetections) {
  // 按照 Y 坐标排序，确保文本按照正确的顺序组合
  const sortedDetections = [...textDetections].sort((a, b) => {
    // 如果 Y 坐标相差不大，则按照 X 坐标排序
    if (Math.abs(a.ItemPolygon.Y - b.ItemPolygon.Y) < 15) {
      return a.ItemPolygon.X - b.ItemPolygon.X;
    }
    return a.ItemPolygon.Y - b.ItemPolygon.Y;
  });

  let fullText = '';
  let currentY = -1;
  let currentLine = '';

  // 按行组织文本，改进行识别逻辑
  sortedDetections.forEach(item => {
    if (currentY === -1 || Math.abs(item.ItemPolygon.Y - currentY) > 15) {
      // 新的一行
      if (currentLine) {
        fullText += currentLine + '\n';
      }
      currentLine = item.DetectedText;
      currentY = item.ItemPolygon.Y;
    } else {
      // 同一行，添加适当的空格
      currentLine += ' ' + item.DetectedText;
    }
  });

  // 添加最后一行
  if (currentLine) {
    fullText += currentLine;
  }

  return fullText.trim();
}

// 辅助函数：处理应用题文本（优化段落格式）
function processApplicationProblemText(text) {
  let processedText = text;

  console.log('原始文本:', processedText);

  // 1. 处理可能的错误格式
  processedText = processedText.replace(/\\\$\$/g, '');  // 移除 \$$
  processedText = processedText.replace(/\$\$\\/g, '');  // 移除 $$\

  // 2. 处理LaTeX命令
  processedText = processedText.replace(/\\text\{([^{}]*)\}/g, '$1');
  processedText = processedText.replace(/\\quad/g, ' ');
  processedText = processedText.replace(/\\begin\{align\*?\}|\\end\{align\*?\}/g, '');

  // 3. 首先清理所有换行符，重新组织文本
  processedText = processedText.replace(/\s*\n\s*/g, ' ');
  processedText = processedText.replace(/\s+/g, ' ');

  // 4. 先处理问题编号，确保前后有适当的空行
  processedText = processedText.replace(/\s*[（(](\d+)[）)]\s*/g, '\n\n($1) ');

  // 5. 智能断句：在句号、感叹号、问号后添加换行
  // 但要避免在问题编号、数字、小数点等情况下断句
  processedText = processedText.replace(/([。！？])\s*(?!\s*$)(?![）)])/g, '$1\n\n');

  // 6. 特殊处理：确保在"平方米。"后面有换行
  processedText = processedText.replace(/(平方米[。！？])\s*(?!\s*$)/g, '$1\n\n');

  // 7. 处理选项标记
  processedText = processedText.replace(/([A-D])\.\s*/g, '\n\n$1. ');

  // 8. 处理题目编号（如"1、"）
  processedText = processedText.replace(/^(\d+[、.])\s*/g, '$1');

  // 9. 清理多余的空行，但保持双换行
  processedText = processedText.replace(/\n\s*\n\s*\n+/g, '\n\n');

  // 10. 确保开头没有多余的空行
  processedText = processedText.replace(/^\s*\n+/, '');

  // 11. 确保结尾没有多余的空行
  processedText = processedText.replace(/\s*$/, '');

  console.log('处理后文本:', processedText);

  return processedText.trim();
}
