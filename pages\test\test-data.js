// 测试数据
module.exports = {
  // 轮船题目测试数据
  shipProblem: `某轮船的静水速度为v千米/时，水流速度为m千米/时，则这艘轮船在两码头间往返一次顺流与逆流的时间比是（）

A. $$\\frac{v+m}{v-m}$$

B. $$\\frac{v-m}{v+m}$$

C. $$\\frac{1}{v} + \\frac{1}{m}$$

D. $$\\frac{v}{m}$$`,

  // 简单数学公式测试
  simpleMath: `$$\\frac{x^2 + 2x + 1}{x + 1}$$`,

  // 复杂应用题测试
  complexProblem: `已知函数f(x) = ax² + bx + c，其中a、b、c为常数，且a ≠ 0。若f(1) = 0，f(2) = 3，f(3) = 8，求f(4)的值。

A. $$15$$

B. $$18$$

C. $$21$$

D. $$24$$`,

  // 三角函数题目
  trigProblem: `在直角三角形ABC中，∠C = 90°，若sin A = $$\\frac{3}{5}$$，则cos B的值为（）

A. $$\\frac{3}{5}$$

B. $$\\frac{4}{5}$$

C. $$\\frac{3}{4}$$

D. $$\\frac{4}{3}$$`
};
