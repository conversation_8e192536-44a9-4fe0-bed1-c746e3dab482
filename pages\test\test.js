// pages/test/test.js
const testData = require('./test-data.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 显示控制
    showShipProblem: false,
    showComplexProblem: false,
    showTrigProblem: false,
    showSimpleMath: false,
    showCustom: false,

    // 测试数据
    shipProblemText: '',
    complexProblemText: '',
    trigProblemText: '',
    simpleMathText: '',
    customInput: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('测试页面加载');

    // 自动加载轮船题目进行测试
    setTimeout(() => {
      this.testShipProblem();
    }, 1000);
  },

  // 测试轮船题目
  testShipProblem: function() {
    console.log('测试轮船题目');
    this.setData({
      shipProblemText: testData.shipProblem,
      showShipProblem: true,
      showComplexProblem: false,
      showTrigProblem: false,
      showSimpleMath: false,
      showCustom: false
    });
  },

  // 测试复杂应用题
  testComplexProblem: function() {
    console.log('测试复杂应用题');
    this.setData({
      complexProblemText: testData.complexProblem,
      showShipProblem: false,
      showComplexProblem: true,
      showTrigProblem: false,
      showSimpleMath: false,
      showCustom: false
    });
  },

  // 测试三角函数题目
  testTrigProblem: function() {
    console.log('测试三角函数题目');
    this.setData({
      trigProblemText: testData.trigProblem,
      showShipProblem: false,
      showComplexProblem: false,
      showTrigProblem: true,
      showSimpleMath: false,
      showCustom: false
    });
  },

  // 测试简单数学公式
  testSimpleMath: function() {
    console.log('测试简单数学公式');
    this.setData({
      simpleMathText: testData.simpleMath,
      showShipProblem: false,
      showComplexProblem: false,
      showTrigProblem: false,
      showSimpleMath: true,
      showCustom: false
    });
  },

  // 自定义输入
  onCustomInput: function(e) {
    this.setData({
      customInput: e.detail.value
    });
  },

  // 测试自定义输入
  testCustomInput: function() {
    if (!this.data.customInput.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    console.log('测试自定义输入:', this.data.customInput);
    this.setData({
      showShipProblem: false,
      showComplexProblem: false,
      showTrigProblem: false,
      showSimpleMath: false,
      showCustom: true
    });
  }
})
